#!/usr/bin/env python3
"""
小红书 x-s-common 请求头生成器 (Python版本)
基于逆向分析的完整补环境实现
"""

import json
import time
import base64
import urllib.parse
import zlib
import re
import hashlib
from typing import Dict, List, Union, Optional

# ==================== 常量定义 ====================

# 版本和密钥常量
RC4_SECRET_VERSION = "1"
RC4_SECRET_VERSION_KEY = "b1b1"
LOCAL_ID_KEY = "a1"
MINI_BROSWER_INFO_KEY = "b1"
SIGN_COUNT_KEY = "sc"
VERSION = "4.0.8"

# URL匹配数组 (变量S)
NEED_XSCOMMON_URLS = [
    "fe_api/burdock/v2/user/keyInfo",
    "fe_api/burdock/v2/shield/profile",
    "fe_api/burdock/v2/shield/captcha",
    "fe_api/burdock/v2/shield/registerCanvas",
    "api/sec/v1/shield/webprofile",
    "api/sec/v1/shield/captcha",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/tags",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/image_stickers",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/other/notes",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/related",
    "/fe_api/burdock/v2/note/post",
    "/api/sns/web",
    "/api/redcaptcha",
    "/api/store/jpd/main"
]

# 实时URL匹配数组 (变量k)
NEED_REAL_TIME_XSCOMMON_URLS = []

# 被阻止的主机列表 (变量g)
BLOCKED_HOSTS = [
    "/t.xiaohongshu.com",
    "/c.xiaohongshu.com",
    "spltest.xiaohongshu.com",
    "t2.xiaohongshu.com",
    "t2-test.xiaohongshu.com",
    "lng.xiaohongshu.com",
    "apm-track.xiaohongshu.com",
    "apm-track-test.xiaohongshu.com",
    "fse.xiaohongshu.com",
    "fse.devops.xiaohongshu.com",
    "fesentry.xiaohongshu.com",
    "spider-tracker.xiaohongshu.com"
]

# PlatformCode枚举
class PlatformCode:
    ANDROID = 1
    IOS = 2
    MACOS = 3
    LINUX = 4
    OTHER = 5

# Base64查找表 (基于code字符串生成)
CODE = "ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5"
LOOKUP = list(CODE)

# ==================== 模拟浏览器环境 ====================

class LocalStorage:
    """模拟localStorage"""
    
    def __init__(self):
        self.storage = {}
    
    def get_item(self, key: str) -> Optional[str]:
        """获取存储项"""
        return self.storage.get(key)
    
    def set_item(self, key: str, value: str) -> None:
        """设置存储项"""
        self.storage[key] = str(value)
    
    def remove_item(self, key: str) -> None:
        """删除存储项"""
        if key in self.storage:
            del self.storage[key]
    
    def clear(self) -> None:
        """清空存储"""
        self.storage.clear()


class SessionStorage:
    """模拟sessionStorage"""
    
    def __init__(self):
        self.storage = {}
    
    def get_item(self, key: str) -> Optional[str]:
        """获取存储项"""
        return self.storage.get(key)
    
    def set_item(self, key: str, value: str) -> None:
        """设置存储项"""
        self.storage[key] = str(value)
    
    def remove_item(self, key: str) -> None:
        """删除存储项"""
        if key in self.storage:
            del self.storage[key]
    
    def clear(self) -> None:
        """清空存储"""
        self.storage.clear()


class JSCookie:
    """模拟js_cookie对象"""
    
    def __init__(self):
        self.cookies = {}
    
    def get(self, key: str) -> str:
        """获取Cookie值"""
        return self.cookies.get(key, "")
    
    def set(self, key: str, value: str) -> None:
        """设置Cookie值"""
        self.cookies[key] = value


class LZObject:
    """模拟l.Z对象"""
    
    def __init__(self, js_cookie: JSCookie):
        self.js_cookie = js_cookie
    
    def get(self, key: str) -> str:
        """获取值"""
        if key == "a1":
            return self.js_cookie.get(key)
        return ""


# 全局环境实例
local_storage = LocalStorage()
session_storage = SessionStorage()
js_cookie = JSCookie()
l_z = LZObject(js_cookie)

# ==================== 核心算法函数 ====================

def get_platform_code(platform: str) -> int:
    """获取平台代码"""
    platform_map = {
        "Android": PlatformCode.ANDROID,
        "iOS": PlatformCode.IOS,
        "Mac OS": PlatformCode.MACOS,
        "Linux": PlatformCode.LINUX
    }
    return platform_map.get(platform, PlatformCode.OTHER)


def get_sig_count(has_sign: bool = False) -> int:
    """获取签名计数"""
    count_str = session_storage.get_item(SIGN_COUNT_KEY)
    count = int(count_str) if count_str else 0
    
    if has_sign:
        count += 1
        session_storage.set_item(SIGN_COUNT_KEY, str(count))
    
    return count


def utils_should_sign(url: str) -> bool:
    """判断是否需要签名"""
    should_sign = True
    
    # 检查是否是本站URL
    if "xiaohongshu.com" in url:
        should_sign = True
    else:
        # 检查是否在阻止列表中
        for host in BLOCKED_HOSTS:
            if host in url:
                should_sign = False
                break
    
    return should_sign


def encode_utf8(text: str) -> List[int]:
    """UTF-8编码函数"""
    encoded = urllib.parse.quote(text, safe='~()*!.\'')
    bytes_list = []
    
    i = 0
    while i < len(encoded):
        char = encoded[i]
        if char == '%':
            hex_str = encoded[i + 1:i + 3]
            byte_val = int(hex_str, 16)
            bytes_list.append(byte_val)
            i += 3
        else:
            bytes_list.append(ord(char))
            i += 1
    
    return bytes_list


def encode_chunk(bytes_data: List[int], start: int, end: int) -> str:
    """Base64编码块函数"""
    result = []
    
    for i in range(start, end, 3):
        a = bytes_data[i] if i < len(bytes_data) else 0
        b = bytes_data[i + 1] if i + 1 < len(bytes_data) else 0
        c = bytes_data[i + 2] if i + 2 < len(bytes_data) else 0
        
        triplet = (a << 16) | (b << 8) | c
        
        result.append(
            LOOKUP[(triplet >> 18) & 63] +
            LOOKUP[(triplet >> 12) & 63] +
            LOOKUP[(triplet >> 6) & 63] +
            LOOKUP[triplet & 63]
        )
    
    return ''.join(result)


def b64_encode(bytes_data: List[int]) -> str:
    """自定义Base64编码函数"""
    length = len(bytes_data)
    remainder = length % 3
    chunks = []
    chunk_size = 16383
    
    # 处理完整的3字节块
    for i in range(0, length - remainder, chunk_size):
        end = min(i + chunk_size, length - remainder)
        chunks.append(encode_chunk(bytes_data, i, end))
    
    # 处理剩余字节
    if remainder == 1:
        last_byte = bytes_data[length - 1]
        chunks.append(
            LOOKUP[last_byte >> 2] +
            LOOKUP[(last_byte << 4) & 63] +
            "=="
        )
    elif remainder == 2:
        second_last = bytes_data[length - 2]
        last = bytes_data[length - 1]
        combined = (second_last << 8) + last
        chunks.append(
            LOOKUP[combined >> 10] +
            LOOKUP[(combined >> 4) & 63] +
            LOOKUP[(combined << 2) & 63] +
            "="
        )
    
    return ''.join(chunks)


def mcr(input_str: str) -> int:
    """CRC32算法 - mcr/O函数的实现"""
    # CRC32查找表
    crc_table = [
        0, 1996959894, 3993919788, 2567524794, 124634137, 1886057615, 3915621685, 2657392035,
        249268274, 2044508324, 3772115230, 2547177864, 162941995, 2125561021, 3887607047, 2428444049,
        498536548, 1789927666, 4089016648, 2227061214, 450548861, 1843258603, 4107580753, 2211677639,
        325883990, 1684777152, 4251122042, 2321926636, 335633487, 1661365465, 4195302755, 2366115317,
        997073096, 1281953886, 3579855332, 2724688242, 1006888145, 1258607687, 3524101629, 2768942443,
        901097722, 1119000684, 3686517206, 2898065728, 853044451, 1172266101, 3705015759, 2882616665,
        651767980, 1373503546, 3369554304, 3218104598, 565507253, 1454621731, 3485111705, 3099436303,
        671266974, 1594198024, 3322730930, 2970347812, 795835527, 1483230225, 3244367275, 3060149565,
        1994146192, 31158534, 2563907772, 4023717930, 1907459465, 112637215, 2680153253, 3904427059,
        2013776290, 251722036, 2517215374, 3775830040, 2137656763, 141376813, 2439277719, 3865271297,
        1802195444, 476864866, 2238001368, 4066508878, 1812370925, 453092731, 2181625025, 4111451223,
        1706088902, 314042704, 2344532202, 4240017532, 1658658271, 366619977, 2362670323, 4224994405,
        1303535960, 984961486, 2747007092, 3569037538, 1256170817, 1037604311, 2765210733, 3554079995,
        1131014506, 879679996, 2909243462, 3663771856, 1141124467, 855842277, 2852801631, 3708648649,
        1342533948, 654459306, 3188396048, 3373015174, 1466479909, 544179635, 3110523913, 3462522015,
        1591671054, 702138776, 2966460450, 3352799412, 1504918807, 783551873, 3082640443, 3233442989,
        3988292384
    ]

    def right_without_sign(num: int, bit: int = 0) -> int:
        """无符号右移"""
        return (num & 0xFFFFFFFF) >> bit

    crc = -1
    max_len = min(57, len(input_str))

    for i in range(max_len):
        byte = ord(input_str[i])
        table_index = (crc & 255) ^ byte
        crc = crc_table[table_index] ^ right_without_sign(crc, 8)

    return (crc ^ -1 ^ 3988292384) & 0xFFFFFFFF


def generate_b1_value() -> str:
    """生成设备指纹 (b1值)"""
    timestamp = str(int(time.time() * 1000))
    device_info = "Windows-Chrome-120.0.0.0"
    combined = f"{device_info}-{timestamp}"

    # 简单的哈希函数模拟
    hash_val = 0
    for char in combined:
        hash_val = ((hash_val << 5) - hash_val) + ord(char)
        hash_val = hash_val & 0xFFFFFFFF  # 转换为32位整数

    return hex(abs(hash_val))[2:][:16]


def check_url_needs_sign(url: str) -> bool:
    """检查URL是否需要签名"""
    for pattern in NEED_XSCOMMON_URLS:
        if isinstance(pattern, str):
            if pattern in url:
                return True
        else:
            # 正则表达式模式
            if re.search(pattern, url):
                return True
    return False


def xs_common(config: Dict, request: Dict) -> Dict:
    """核心函数：生成 x-s-common 请求头"""
    try:
        platform = config.get('platform', 'PC')
        url = request['url']

        # 检查URL是否需要签名
        needs_sign = check_url_needs_sign(url)
        if not needs_sign or not utils_should_sign(url):
            return request

        # 获取现有的签名头
        headers = request.get('headers', {})
        x_sign = headers.get("X-Sign", "")
        x_t = headers.get("X-t", "")
        x_s = headers.get("X-s", "")

        # 获取签名计数
        sig_count = get_sig_count(bool(x_sign or x_t or x_s))

        # 获取存储的值
        b1_value = local_storage.get_item(MINI_BROSWER_INFO_KEY) or generate_b1_value()
        b1b1_value = local_storage.get_item(RC4_SECRET_VERSION_KEY) or RC4_SECRET_VERSION
        a1_value = l_z.get(LOCAL_ID_KEY) or ""

        # 存储b1值
        local_storage.set_item(MINI_BROSWER_INFO_KEY, b1_value)

        # 构建x-s-common数据结构
        common_data = {
            "s0": get_platform_code(platform),
            "s1": "",
            "x0": b1b1_value,
            "x1": VERSION,
            "x2": platform or "PC",
            "x3": "xhs-pc-web",
            "x4": "4.68.0",
            "x5": a1_value,
            "x6": x_t,
            "x7": x_s,
            "x8": b1_value,
            "x9": mcr(f"{x_t}{x_s}{b1_value}"),
            "x10": sig_count,
            "x11": "normal"
        }

        # 检查是否需要实时更新
        needs_real_time = False
        for pattern in NEED_REAL_TIME_XSCOMMON_URLS:
            if isinstance(pattern, str):
                if pattern in url:
                    needs_real_time = True
                    break
            else:
                if re.search(pattern, url):
                    needs_real_time = True
                    break

        # 生成x-s-common
        json_str = json.dumps(common_data, separators=(',', ':'))
        utf8_bytes = encode_utf8(json_str)
        x_s_common = b64_encode(utf8_bytes)

        # 设置X-S-Common头
        if 'headers' not in request:
            request['headers'] = {}
        request['headers']["X-S-Common"] = x_s_common

        return request

    except Exception as e:
        print(f"生成x-s-common失败: {e}")
        return request


def generate_xs_common(url: str, options: Optional[Dict] = None) -> Optional[str]:
    """简化的生成函数，直接返回x-s-common值"""
    if options is None:
        options = {}

    config = {
        'platform': options.get('platform', 'PC')
    }

    request = {
        'url': url,
        'headers': {
            "X-Sign": options.get('xSign', ''),
            "X-t": options.get('xT', ''),
            "X-s": options.get('xS', '')
        }
    }

    result = xs_common(config, request)
    return result.get('headers', {}).get("X-S-Common")


def set_user_data(user_data: Dict) -> None:
    """设置用户数据"""
    if 'a1' in user_data:
        js_cookie.set('a1', user_data['a1'])

    if 'b1' in user_data:
        local_storage.set_item(MINI_BROSWER_INFO_KEY, user_data['b1'])

    if 'b1b1' in user_data:
        local_storage.set_item(RC4_SECRET_VERSION_KEY, user_data['b1b1'])


def parse_cookie(cookie_string: str) -> Dict[str, str]:
    """解析cookie字符串"""
    cookies = {}
    for cookie_pair in cookie_string.split(';'):
        if '=' in cookie_pair:
            name, value = cookie_pair.strip().split('=', 1)
            if name and value:
                cookies[name] = urllib.parse.unquote(value)
    return cookies


def test():
    """测试函数"""
    print("🧪 测试 x-s-common 生成器 (Python版本)")
    print("=" * 60)

    # 使用真实的cookie数据
    real_cookie = ("abRequestId=41b545ec-e397-57bd-9d91-3569da43d3d0; xsecappid=xhs-pc-web; "
                   "a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; "
                   "webId=00ec9cba945a033e639b90fd1084ed06; "
                   "gid=yjWJjS02JiIYyjWJjS0qd6TkfjiWy0UCVuC2V763I00KyE28CkS0FD8882KYy2J804SS4KWf; "
                   "web_session=040069b2f9b2fb2f11df169d7f3a4bba9874fc; webBuild=4.68.0")

    cookies = parse_cookie(real_cookie)
    print("\n📋 解析的Cookie数据:")
    print(f"  a1: {cookies.get('a1', 'N/A')}")
    print(f"  webId: {cookies.get('webId', 'N/A')}")
    print(f"  gid: {cookies.get('gid', 'N/A')}")
    print(f"  webBuild: {cookies.get('webBuild', 'N/A')}")

    # 设置真实数据
    set_user_data({
        'a1': cookies.get('a1', ''),
        'b1': cookies.get('webId', ''),  # 使用webId作为设备指纹
        'b1b1': "1"
    })

    print("\n✅ 用户数据设置完成")

    # 测试URL
    test_urls = [
        "https://www.xiaohongshu.com/api/sns/web/v1/feed",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/note/post",
        "https://www.xiaohongshu.com/api/sec/v1/shield/webprofile"
    ]

    print("\n🧪 开始测试生成 x-s-common...")

    for index, url in enumerate(test_urls):
        print(f"\n📝 测试 {index + 1}: {url}")

        xs_common_result = generate_xs_common(url, {
            'platform': "PC",
            'xT': str(int(time.time() * 1000)),
            'xS': "test_xs_value"
        })

        if xs_common_result:
            print(f"✅ 生成成功: {xs_common_result[:50]}...")
            print(f"   完整长度: {len(xs_common_result)} 字符")

            # 尝试解码验证
            try:
                decoded = base64.b64decode(xs_common_result).decode('utf-8')
                parsed = json.loads(decoded)
                print(f"📊 解码验证成功:")
                print(f"   s0 (平台代码): {parsed.get('s0')}")
                print(f"   x1 (版本): {parsed.get('x1')}")
                print(f"   x2 (平台): {parsed.get('x2')}")
                print(f"   x3 (应用): {parsed.get('x3')}")
                print(f"   x4 (应用版本): {parsed.get('x4')}")
                print(f"   x10 (计数): {parsed.get('x10')}")
                print(f"   x11 (状态): {parsed.get('x11')}")
            except Exception as decode_error:
                print(f"❌ 解码失败: {decode_error}")
        else:
            print(f"❌ 生成失败")

    # 测试CRC32算法
    print("\n🔧 测试CRC32算法:")
    test_strings = [
        "test",
        "hello world",
        f"{int(time.time() * 1000)}test{cookies.get('webId', '')}",
        ""
    ]

    for test_str in test_strings:
        crc = mcr(test_str)
        print(f"   \"{test_str}\" -> {crc}")

    # 测试Base64编码
    print("\n🔧 测试自定义Base64编码:")
    test_data = "Hello, 小红书!"
    utf8_bytes = encode_utf8(test_data)
    base64_result = b64_encode(utf8_bytes)
    print(f"   原文: {test_data}")
    print(f"   UTF8字节: {utf8_bytes[:10]}...")
    print(f"   Base64: {base64_result}")

    # 与标准Base64对比
    standard_base64 = base64.b64encode(test_data.encode('utf-8')).decode('ascii')
    print(f"   标准Base64: {standard_base64}")
    print(f"   是否相同: {'✅是' if base64_result == standard_base64 else '❌否'}")

    print("\n🎯 测试完成！")
    print("\n💡 使用建议:")
    print("1. 确保a1 cookie值是最新的")
    print("2. webId应该保持一致作为设备指纹")
    print("3. X-t和X-s值需要与实际请求匹配")
    print("4. 生成的x-s-common应该在请求中作为请求头使用")


def test_single_url(url: str, options: Optional[Dict] = None) -> Optional[str]:
    """单独测试特定URL"""
    if options is None:
        options = {}

    print(f"\n🎯 单独测试URL: {url}")

    # 使用测试cookie
    real_cookie = ("a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; "
                   "webId=00ec9cba945a033e639b90fd1084ed06")
    cookies = parse_cookie(real_cookie)

    set_user_data({
        'a1': cookies.get('a1', ''),
        'b1': cookies.get('webId', ''),
        'b1b1': "1"
    })

    timestamp = str(int(time.time() * 1000))
    result = generate_xs_common(url, {
        'platform': options.get('platform', 'PC'),
        'xT': options.get('xT', timestamp),
        'xS': options.get('xS', f'test_xs_{timestamp}')
    })

    if result:
        print(f"✅ 生成成功:")
        print(f"X-S-Common: {result}")

        print(f"\n📋 完整请求头示例:")
        print(f"X-S-Common: {result}")
        print(f"X-t: {options.get('xT', timestamp)}")
        print(f"X-s: {options.get('xS', f'test_xs_{timestamp}')}")
        print(f"Cookie: {real_cookie}")
        print(f"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    else:
        print(f"❌ 生成失败")

    return result


if __name__ == '__main__':
    import sys

    # 命令行参数处理
    args = sys.argv[1:]

    if len(args) > 0:
        url = args[0]
        platform = args[1] if len(args) > 1 else "PC"
        test_single_url(url, {'platform': platform})
    else:
        test()

    # 如果有额外参数，生成特定URL的签名
    if len(args) > 0:
        url = args[0]
        platform = args[1] if len(args) > 1 else "PC"

        print(f"\n🚀 生成 x-s-common for: {url}")
        result = generate_xs_common(url, {'platform': platform})

        if result:
            print(f"✅ 结果: {result}")
        else:
            print(f"❌ 生成失败")
