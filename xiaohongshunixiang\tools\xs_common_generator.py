#!/usr/bin/env python3
"""
小红书 x-s-common 请求头生成器 (Python版本)
基于逆向分析的完整补环境实现
"""

import json
import time
import base64
import urllib.parse
import zlib
import re
import hashlib
from typing import Dict, List, Union, Optional

# ==================== 常量定义 ====================

# 版本和密钥常量
RC4_SECRET_VERSION = "1"
RC4_SECRET_VERSION_KEY = "b1b1"
LOCAL_ID_KEY = "a1"
MINI_BROSWER_INFO_KEY = "b1"
SIGN_COUNT_KEY = "sc"
VERSION = "4.0.8"

# URL匹配数组 (变量S)
NEED_XSCOMMON_URLS = [
    "fe_api/burdock/v2/user/keyInfo",
    "fe_api/burdock/v2/shield/profile",
    "fe_api/burdock/v2/shield/captcha",
    "fe_api/burdock/v2/shield/registerCanvas",
    "api/sec/v1/shield/webprofile",
    "api/sec/v1/shield/captcha",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/tags",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/image_stickers",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/other/notes",
    r"fe_api/burdock/v2/note/[0-9a-zA-Z]+/related",
    "/fe_api/burdock/v2/note/post",
    "/api/sns/web",
    "/api/redcaptcha",
    "/api/store/jpd/main"
]

# 实时URL匹配数组 (变量k)
NEED_REAL_TIME_XSCOMMON_URLS = []

# 被阻止的主机列表 (变量g)
BLOCKED_HOSTS = [
    "/t.xiaohongshu.com",
    "/c.xiaohongshu.com",
    "spltest.xiaohongshu.com",
    "t2.xiaohongshu.com",
    "t2-test.xiaohongshu.com",
    "lng.xiaohongshu.com",
    "apm-track.xiaohongshu.com",
    "apm-track-test.xiaohongshu.com",
    "fse.xiaohongshu.com",
    "fse.devops.xiaohongshu.com",
    "fesentry.xiaohongshu.com",
    "spider-tracker.xiaohongshu.com"
]

# PlatformCode枚举
class PlatformCode:
    ANDROID = 1
    IOS = 2
    MACOS = 3
    LINUX = 4
    OTHER = 5

# Base64查找表 (基于code字符串生成)
CODE = "ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5"
LOOKUP = list(CODE)

# ==================== 模拟浏览器环境 ====================

class LocalStorage:
    """模拟localStorage"""
    
    def __init__(self):
        self.storage = {}
    
    def get_item(self, key: str) -> Optional[str]:
        """获取存储项"""
        return self.storage.get(key)
    
    def set_item(self, key: str, value: str) -> None:
        """设置存储项"""
        self.storage[key] = str(value)
    
    def remove_item(self, key: str) -> None:
        """删除存储项"""
        if key in self.storage:
            del self.storage[key]
    
    def clear(self) -> None:
        """清空存储"""
        self.storage.clear()


class SessionStorage:
    """模拟sessionStorage"""
    
    def __init__(self):
        self.storage = {}
    
    def get_item(self, key: str) -> Optional[str]:
        """获取存储项"""
        return self.storage.get(key)
    
    def set_item(self, key: str, value: str) -> None:
        """设置存储项"""
        self.storage[key] = str(value)
    
    def remove_item(self, key: str) -> None:
        """删除存储项"""
        if key in self.storage:
            del self.storage[key]
    
    def clear(self) -> None:
        """清空存储"""
        self.storage.clear()


class JSCookie:
    """模拟js_cookie对象"""
    
    def __init__(self):
        self.cookies = {}
    
    def get(self, key: str) -> str:
        """获取Cookie值"""
        return self.cookies.get(key, "")
    
    def set(self, key: str, value: str) -> None:
        """设置Cookie值"""
        self.cookies[key] = value


class LZObject:
    """模拟l.Z对象"""
    
    def __init__(self, js_cookie: JSCookie):
        self.js_cookie = js_cookie
    
    def get(self, key: str) -> str:
        """获取值"""
        if key == "a1":
            return self.js_cookie.get(key)
        return ""


# 全局环境实例
local_storage = LocalStorage()
session_storage = SessionStorage()
js_cookie = JSCookie()
l_z = LZObject(js_cookie)

# ==================== 核心算法函数 ====================

def get_platform_code(platform: str) -> int:
    """获取平台代码"""
    platform_map = {
        "Android": PlatformCode.ANDROID,
        "iOS": PlatformCode.IOS,
        "Mac OS": PlatformCode.MACOS,
        "Linux": PlatformCode.LINUX
    }
    return platform_map.get(platform, PlatformCode.OTHER)


def get_sig_count(has_sign: bool = False) -> int:
    """获取签名计数"""
    count_str = session_storage.get_item(SIGN_COUNT_KEY)
    count = int(count_str) if count_str else 0
    
    if has_sign:
        count += 1
        session_storage.set_item(SIGN_COUNT_KEY, str(count))
    
    return count


def utils_should_sign(url: str) -> bool:
    """判断是否需要签名"""
    should_sign = True
    
    # 检查是否是本站URL
    if "xiaohongshu.com" in url:
        should_sign = True
    else:
        # 检查是否在阻止列表中
        for host in BLOCKED_HOSTS:
            if host in url:
                should_sign = False
                break
    
    return should_sign


def encode_utf8(text: str) -> List[int]:
    """UTF-8编码函数"""
    encoded = urllib.parse.quote(text, safe='~()*!.\'')
    bytes_list = []
    
    i = 0
    while i < len(encoded):
        char = encoded[i]
        if char == '%':
            hex_str = encoded[i + 1:i + 3]
            byte_val = int(hex_str, 16)
            bytes_list.append(byte_val)
            i += 3
        else:
            bytes_list.append(ord(char))
            i += 1
    
    return bytes_list


def encode_chunk(bytes_data: List[int], start: int, end: int) -> str:
    """Base64编码块函数"""
    result = []

    for i in range(start, min(end, len(bytes_data)), 3):
        a = bytes_data[i] if i < len(bytes_data) else 0
        b = bytes_data[i + 1] if i + 1 < len(bytes_data) else 0
        c = bytes_data[i + 2] if i + 2 < len(bytes_data) else 0

        triplet = (a << 16) | (b << 8) | c

        # 安全检查索引
        idx1 = (triplet >> 18) & 63
        idx2 = (triplet >> 12) & 63
        idx3 = (triplet >> 6) & 63
        idx4 = triplet & 63

        if all(idx < len(LOOKUP) for idx in [idx1, idx2, idx3, idx4]):
            result.append(
                LOOKUP[idx1] +
                LOOKUP[idx2] +
                LOOKUP[idx3] +
                LOOKUP[idx4]
            )

    return ''.join(result)


def b64_encode(bytes_data: List[int]) -> str:
    """自定义Base64编码函数"""
    if not bytes_data:
        return ""

    length = len(bytes_data)
    remainder = length % 3
    chunks = []
    chunk_size = 16383

    # 处理完整的3字节块
    main_length = length - remainder
    for i in range(0, main_length, chunk_size):
        end = min(i + chunk_size, main_length)
        chunks.append(encode_chunk(bytes_data, i, end))

    # 处理剩余字节
    if remainder == 1:
        last_byte = bytes_data[length - 1]
        idx1 = last_byte >> 2
        idx2 = (last_byte << 4) & 63
        if idx1 < len(LOOKUP) and idx2 < len(LOOKUP):
            chunks.append(
                LOOKUP[idx1] +
                LOOKUP[idx2] +
                "=="
            )
    elif remainder == 2:
        second_last = bytes_data[length - 2]
        last = bytes_data[length - 1]
        combined = (second_last << 8) + last
        idx1 = combined >> 10
        idx2 = (combined >> 4) & 63
        idx3 = (combined << 2) & 63
        if all(idx < len(LOOKUP) for idx in [idx1, idx2, idx3]):
            chunks.append(
                LOOKUP[idx1] +
                LOOKUP[idx2] +
                LOOKUP[idx3] +
                "="
            )

    return ''.join(chunks)


def mcr(input_str: str) -> int:
    """CRC32算法 - mcr/O函数的实现"""
    # 完整的CRC32查找表 (256个元素)
    crc_table = [
        0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA, 0x076DC419, 0x706AF48F,
        0xE963A535, 0x9E6495A3, 0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988,
        0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91, 0x1DB71064, 0x6AB020F2,
        0xF3B97148, 0x84BE41DE, 0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
        0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC, 0x14015C4F, 0x63066CD9,
        0xFA0F3D63, 0x8D080DF5, 0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172,
        0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B, 0x35B5A8FA, 0x42B2986C,
        0xDBBBC9D6, 0xACBCF940, 0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,
        0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116, 0x21B4F4B5, 0x56B3C423,
        0xCFBA9599, 0xB8BDA50F, 0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924,
        0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D, 0x76DC4190, 0x01DB7106,
        0x98D220BC, 0xEFD5102A, 0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
        0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818, 0x7F6A0DBB, 0x086D3D2D,
        0x91646C97, 0xE6635C01, 0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E,
        0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457, 0x65B0D9C6, 0x12B7E950,
        0x8BBEB8EA, 0xFCB9887C, 0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,
        0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2, 0x4ADFA541, 0x3DD895D7,
        0xA4D1C46D, 0xD3D6F4FB, 0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0,
        0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9, 0x5005713C, 0x270241AA,
        0xBE0B1010, 0xC90C2086, 0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
        0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4, 0x59B33D17, 0x2EB40D81,
        0xB7BD5C3B, 0xC0BA6CAD, 0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A,
        0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683, 0xE3630B12, 0x94643B84,
        0x0D6D6A3E, 0x7A6A5AA8, 0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,
        0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE, 0xF762575D, 0x806567CB,
        0x196C3671, 0x6E6B06E7, 0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC,
        0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5, 0xD6D6A3E8, 0xA1D1937E,
        0x38D8C2C4, 0x4FDFF252, 0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
        0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60, 0xDF60EFC3, 0xA867DF55,
        0x316E8EEF, 0x4669BE79, 0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236,
        0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F, 0xC5BA3BBE, 0xB2BD0B28,
        0x2BB45A92, 0x5CB36A04, 0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,
        0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A, 0x9C0906A9, 0xEB0E363F,
        0x72076785, 0x05005713, 0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38,
        0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21, 0x86D3D2D4, 0xF1D4E242,
        0x68DDB3F8, 0x1FDA836E, 0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
        0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C, 0x8F659EFF, 0xF862AE69,
        0x616BFFD3, 0x166CCF45, 0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2,
        0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB, 0xAED16A4A, 0xD9D65ADC,
        0x40DF0B66, 0x37D83BF0, 0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,
        0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6, 0xBAD03605, 0xCDD70693,
        0x54DE5729, 0x23D967BF, 0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94,
        0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D
    ]

    def right_without_sign(num: int, bit: int = 0) -> int:
        """无符号右移"""
        return (num & 0xFFFFFFFF) >> bit

    crc = 0xFFFFFFFF  # 初始值改为标准CRC32初始值
    max_len = min(57, len(input_str))

    for i in range(max_len):
        byte = ord(input_str[i])
        table_index = (crc & 0xFF) ^ byte
        if table_index < len(crc_table):  # 安全检查
            crc = crc_table[table_index] ^ right_without_sign(crc, 8)

    return (crc ^ 0xFFFFFFFF ^ 3988292384) & 0xFFFFFFFF


def generate_b1_value() -> str:
    """生成设备指纹 (b1值)"""
    timestamp = str(int(time.time() * 1000))
    device_info = "Windows-Chrome-120.0.0.0"
    combined = f"{device_info}-{timestamp}"

    # 简单的哈希函数模拟
    hash_val = 0
    for char in combined:
        hash_val = ((hash_val << 5) - hash_val) + ord(char)
        hash_val = hash_val & 0xFFFFFFFF  # 转换为32位整数

    return hex(abs(hash_val))[2:][:16]


def check_url_needs_sign(url: str) -> bool:
    """检查URL是否需要签名"""
    for pattern in NEED_XSCOMMON_URLS:
        if isinstance(pattern, str):
            if pattern in url:
                return True
        else:
            # 正则表达式模式
            if re.search(pattern, url):
                return True
    return False


def xs_common(config: Dict, request: Dict) -> Dict:
    """核心函数：生成 x-s-common 请求头"""
    try:
        platform = config.get('platform', 'PC')
        url = request['url']

        # 检查URL是否需要签名
        needs_sign = check_url_needs_sign(url)
        if not needs_sign or not utils_should_sign(url):
            return request

        # 获取现有的签名头
        headers = request.get('headers', {})
        x_sign = headers.get("X-Sign", "")
        x_t = headers.get("X-t", "")
        x_s = headers.get("X-s", "")

        # 获取签名计数
        sig_count = get_sig_count(bool(x_sign or x_t or x_s))

        # 获取存储的值
        b1_value = local_storage.get_item(MINI_BROSWER_INFO_KEY) or generate_b1_value()
        b1b1_value = local_storage.get_item(RC4_SECRET_VERSION_KEY) or RC4_SECRET_VERSION
        a1_value = l_z.get(LOCAL_ID_KEY) or ""

        # 存储b1值
        local_storage.set_item(MINI_BROSWER_INFO_KEY, b1_value)

        # 构建x-s-common数据结构
        common_data = {
            "s0": get_platform_code(platform),
            "s1": "",
            "x0": b1b1_value,
            "x1": VERSION,
            "x2": platform or "PC",
            "x3": "xhs-pc-web",
            "x4": "4.68.0",
            "x5": a1_value,
            "x6": x_t,
            "x7": x_s,
            "x8": b1_value,
            "x9": mcr(f"{x_t}{x_s}{b1_value}"),
            "x10": sig_count,
            "x11": "normal"
        }

        # 检查是否需要实时更新
        needs_real_time = False
        for pattern in NEED_REAL_TIME_XSCOMMON_URLS:
            if isinstance(pattern, str):
                if pattern in url:
                    needs_real_time = True
                    break
            else:
                if re.search(pattern, url):
                    needs_real_time = True
                    break

        # 生成x-s-common
        json_str = json.dumps(common_data, separators=(',', ':'))
        utf8_bytes = encode_utf8(json_str)
        x_s_common = b64_encode(utf8_bytes)

        # 设置X-S-Common头
        if 'headers' not in request:
            request['headers'] = {}
        request['headers']["X-S-Common"] = x_s_common

        return request

    except Exception as e:
        print(f"生成x-s-common失败: {e}")
        return request


def generate_xs_common(url: str, options: Optional[Dict] = None) -> Optional[str]:
    """简化的生成函数，直接返回x-s-common值"""
    if options is None:
        options = {}

    config = {
        'platform': options.get('platform', 'PC')
    }

    request = {
        'url': url,
        'headers': {
            "X-Sign": options.get('xSign', ''),
            "X-t": options.get('xT', ''),
            "X-s": options.get('xS', '')
        }
    }

    result = xs_common(config, request)
    return result.get('headers', {}).get("X-S-Common")


def set_user_data(user_data: Dict) -> None:
    """设置用户数据"""
    if 'a1' in user_data:
        js_cookie.set('a1', user_data['a1'])

    if 'b1' in user_data:
        local_storage.set_item(MINI_BROSWER_INFO_KEY, user_data['b1'])

    if 'b1b1' in user_data:
        local_storage.set_item(RC4_SECRET_VERSION_KEY, user_data['b1b1'])


def parse_cookie(cookie_string: str) -> Dict[str, str]:
    """解析cookie字符串"""
    cookies = {}
    for cookie_pair in cookie_string.split(';'):
        if '=' in cookie_pair:
            name, value = cookie_pair.strip().split('=', 1)
            if name and value:
                cookies[name] = urllib.parse.unquote(value)
    return cookies


def test():
    """测试函数"""
    print("🧪 测试 x-s-common 生成器 (Python版本)")
    print("=" * 60)

    # 使用真实的cookie数据
    real_cookie = ("abRequestId=41b545ec-e397-57bd-9d91-3569da43d3d0; xsecappid=xhs-pc-web; "
                   "a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; "
                   "webId=00ec9cba945a033e639b90fd1084ed06; "
                   "gid=yjWJjS02JiIYyjWJjS0qd6TkfjiWy0UCVuC2V763I00KyE28CkS0FD8882KYy2J804SS4KWf; "
                   "web_session=040069b2f9b2fb2f11df169d7f3a4bba9874fc; webBuild=4.68.0")

    cookies = parse_cookie(real_cookie)
    print("\n📋 解析的Cookie数据:")
    print(f"  a1: {cookies.get('a1', 'N/A')}")
    print(f"  webId: {cookies.get('webId', 'N/A')}")
    print(f"  gid: {cookies.get('gid', 'N/A')}")
    print(f"  webBuild: {cookies.get('webBuild', 'N/A')}")

    # 设置真实数据
    set_user_data({
        'a1': cookies.get('a1', ''),
        'b1': cookies.get('webId', ''),  # 使用webId作为设备指纹
        'b1b1': "1"
    })

    print("\n✅ 用户数据设置完成")

    # 测试URL
    test_urls = [
        "https://www.xiaohongshu.com/api/sns/web/v1/feed",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/note/post",
        "https://www.xiaohongshu.com/api/sec/v1/shield/webprofile"
    ]

    print("\n🧪 开始测试生成 x-s-common...")

    for index, url in enumerate(test_urls):
        print(f"\n📝 测试 {index + 1}: {url}")

        xs_common_result = generate_xs_common(url, {
            'platform': "PC",
            'xT': str(int(time.time() * 1000)),
            'xS': "test_xs_value"
        })

        if xs_common_result:
            print(f"✅ 生成成功: {xs_common_result[:50]}...")
            print(f"   完整长度: {len(xs_common_result)} 字符")

            # 尝试解码验证
            try:
                decoded = base64.b64decode(xs_common_result).decode('utf-8')
                parsed = json.loads(decoded)
                print(f"📊 解码验证成功:")
                print(f"   s0 (平台代码): {parsed.get('s0')}")
                print(f"   x1 (版本): {parsed.get('x1')}")
                print(f"   x2 (平台): {parsed.get('x2')}")
                print(f"   x3 (应用): {parsed.get('x3')}")
                print(f"   x4 (应用版本): {parsed.get('x4')}")
                print(f"   x10 (计数): {parsed.get('x10')}")
                print(f"   x11 (状态): {parsed.get('x11')}")
            except Exception as decode_error:
                print(f"❌ 解码失败: {decode_error}")
        else:
            print(f"❌ 生成失败")

    # 测试CRC32算法
    print("\n🔧 测试CRC32算法:")
    test_strings = [
        "test",
        "hello world",
        f"{int(time.time() * 1000)}test{cookies.get('webId', '')}",
        ""
    ]

    for test_str in test_strings:
        crc = mcr(test_str)
        print(f"   \"{test_str}\" -> {crc}")

    # 测试Base64编码
    print("\n🔧 测试自定义Base64编码:")
    test_data = "Hello, 小红书!"
    utf8_bytes = encode_utf8(test_data)
    base64_result = b64_encode(utf8_bytes)
    print(f"   原文: {test_data}")
    print(f"   UTF8字节: {utf8_bytes[:10]}...")
    print(f"   Base64: {base64_result}")

    # 与标准Base64对比
    standard_base64 = base64.b64encode(test_data.encode('utf-8')).decode('ascii')
    print(f"   标准Base64: {standard_base64}")
    print(f"   是否相同: {'✅是' if base64_result == standard_base64 else '❌否'}")

    print("\n🎯 测试完成！")
    print("\n💡 使用建议:")
    print("1. 确保a1 cookie值是最新的")
    print("2. webId应该保持一致作为设备指纹")
    print("3. X-t和X-s值需要与实际请求匹配")
    print("4. 生成的x-s-common应该在请求中作为请求头使用")


def test_single_url(url: str, options: Optional[Dict] = None) -> Optional[str]:
    """单独测试特定URL"""
    if options is None:
        options = {}

    print(f"\n🎯 单独测试URL: {url}")

    # 使用测试cookie
    real_cookie = ("a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; "
                   "webId=00ec9cba945a033e639b90fd1084ed06")
    cookies = parse_cookie(real_cookie)

    set_user_data({
        'a1': cookies.get('a1', ''),
        'b1': cookies.get('webId', ''),
        'b1b1': "1"
    })

    timestamp = str(int(time.time() * 1000))
    result = generate_xs_common(url, {
        'platform': options.get('platform', 'PC'),
        'xT': options.get('xT', timestamp),
        'xS': options.get('xS', f'test_xs_{timestamp}')
    })

    if result:
        print(f"✅ 生成成功:")
        print(f"X-S-Common: {result}")

        print(f"\n📋 完整请求头示例:")
        print(f"X-S-Common: {result}")
        print(f"X-t: {options.get('xT', timestamp)}")
        print(f"X-s: {options.get('xS', f'test_xs_{timestamp}')}")
        print(f"Cookie: {real_cookie}")
        print(f"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    else:
        print(f"❌ 生成失败")

    return result


if __name__ == '__main__':
    import sys

    # 命令行参数处理
    args = sys.argv[1:]

    if len(args) > 0:
        url = args[0]
        platform = args[1] if len(args) > 1 else "PC"
        test_single_url(url, {'platform': platform})
    else:
        test()

    # 如果有额外参数，生成特定URL的签名
    if len(args) > 0:
        url = args[0]
        platform = args[1] if len(args) > 1 else "PC"

        print(f"\n🚀 生成 x-s-common for: {url}")
        result = generate_xs_common(url, {'platform': platform})

        if result:
            print(f"✅ 结果: {result}")
        else:
            print(f"❌ 生成失败")
