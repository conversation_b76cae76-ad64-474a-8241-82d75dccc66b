#!/usr/bin/env python3
"""
浏览器JS代码执行器
- 加载从浏览器扒下来的原始JS代码
- 用Python提供环境补全
- 保持算法100%原汁原味
"""

import json
import subprocess
import tempfile
import os
import time
from typing import Dict, Optional

class BrowserJSExecutor:
    """浏览器JS代码执行器"""
    
    def __init__(self):
        self.localStorage = {}
        self.sessionStorage = {}
        self.cookies = {}
        
    def load_original_js_file(self, js_file_path: str) -> str:
        """加载原始的JS文件"""
        try:
            with open(js_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ 加载JS文件失败: {e}")
            return ""
    
    def create_environment_wrapper(self, original_js: str) -> str:
        """为原始JS代码创建环境包装器"""
        wrapper = f'''
// ==================== Python环境注入 ====================
var pythonEnv = JSON.parse(process.argv[2]);
var requestParams = JSON.parse(process.argv[3]);

// 模拟localStorage
var localStorage = {{
    getItem: function(key) {{
        return pythonEnv.localStorage[key] || null;
    }},
    setItem: function(key, value) {{
        pythonEnv.localStorage[key] = String(value);
    }},
    removeItem: function(key) {{
        delete pythonEnv.localStorage[key];
    }}
}};

// 模拟sessionStorage
var sessionStorage = {{
    getItem: function(key) {{
        return pythonEnv.sessionStorage[key] || null;
    }},
    setItem: function(key, value) {{
        pythonEnv.sessionStorage[key] = String(value);
    }},
    removeItem: function(key) {{
        delete pythonEnv.sessionStorage[key];
    }}
}};

// 模拟document.cookie
var document = {{
    cookie: pythonEnv.cookieString || ""
}};

// 模拟js-cookie库
var Cookies = {{
    get: function(name) {{
        return pythonEnv.cookies[name] || "";
    }},
    set: function(name, value) {{
        pythonEnv.cookies[name] = value;
    }}
}};

// 模拟小红书特有的cookie访问方式
var js_cookie = {{
    A: {{
        get: function(key) {{
            return pythonEnv.cookies[key] || "";
        }}
    }}
}};

// 模拟l.Z对象
var l = {{
    Z: {{
        get: function(key) {{
            if (key === "a1") {{
                return js_cookie.A.get(key);
            }}
            return "";
        }}
    }}
}};

// 模拟window对象的部分属性
var window = {{
    localStorage: localStorage,
    sessionStorage: sessionStorage,
    navigator: {{
        userAgent: pythonEnv.userAgent || "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }}
}};

// ==================== 原始浏览器JS代码 ====================
{original_js}

// ==================== 执行接口 ====================
try {{
    var result;
    
    // 根据请求类型执行不同的函数
    if (requestParams.action === "generateXSCommon") {{
        // 如果原始代码中有xsCommon函数，调用它
        if (typeof xsCommon === 'function') {{
            var config = {{ platform: requestParams.platform || "PC" }};
            var request = {{
                url: requestParams.url,
                headers: {{
                    "X-Sign": requestParams.xSign || "",
                    "X-t": requestParams.xT || "",
                    "X-s": requestParams.xS || ""
                }}
            }};
            var modifiedRequest = xsCommon(config, request);
            result = modifiedRequest.headers["X-S-Common"] || null;
        }}
        // 或者调用其他可能的函数名
        else if (typeof generateXSCommon === 'function') {{
            result = generateXSCommon(requestParams.url, requestParams.options || {{}});
        }}
        // 或者调用_webmsxyw函数
        else if (typeof _webmsxyw === 'function') {{
            var signResult = _webmsxyw(requestParams.url, requestParams.data || {{}});
            result = signResult;
        }}
        else {{
            result = null;
        }}
    }}
    else if (requestParams.action === "callFunction") {{
        // 直接调用指定的函数
        var funcName = requestParams.functionName;
        var args = requestParams.arguments || [];
        
        if (typeof eval(funcName) === 'function') {{
            result = eval(funcName).apply(null, args);
        }} else {{
            result = null;
        }}
    }}
    
    // 返回结果和更新后的环境
    console.log(JSON.stringify({{
        success: result !== null && result !== undefined,
        result: result,
        updatedEnv: {{
            localStorage: pythonEnv.localStorage,
            sessionStorage: pythonEnv.sessionStorage,
            cookies: pythonEnv.cookies
        }},
        error: null
    }}));
    
}} catch (error) {{
    console.log(JSON.stringify({{
        success: false,
        result: null,
        updatedEnv: null,
        error: error.message
    }}));
}}
'''
        return wrapper
    
    def execute_js(self, js_code: str, action: str, params: Dict) -> Dict:
        """执行JavaScript代码"""
        try:
            # 准备环境数据
            env_data = {
                'localStorage': self.localStorage.copy(),
                'sessionStorage': self.sessionStorage.copy(),
                'cookies': self.cookies.copy(),
                'cookieString': self._build_cookie_string(),
                'userAgent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }
            
            # 准备请求参数
            request_params = {
                'action': action,
                **params
            }
            
            # 创建包装后的JS代码
            wrapped_js = self.create_environment_wrapper(js_code)
            
            # 创建临时文件并执行
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False, encoding='utf-8') as f:
                f.write(wrapped_js)
                js_file = f.name
            
            try:
                # 执行Node.js
                cmd = ['node', js_file, json.dumps(env_data), json.dumps(request_params)]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    output = json.loads(result.stdout.strip())
                    
                    # 更新环境状态
                    if output.get('updatedEnv'):
                        self.localStorage.update(output['updatedEnv']['localStorage'])
                        self.sessionStorage.update(output['updatedEnv']['sessionStorage'])
                        self.cookies.update(output['updatedEnv']['cookies'])
                    
                    return output
                else:
                    return {
                        'success': False,
                        'result': None,
                        'error': f"Node.js执行失败: {result.stderr}"
                    }
                    
            finally:
                # 清理临时文件
                if os.path.exists(js_file):
                    os.unlink(js_file)
                    
        except Exception as e:
            return {
                'success': False,
                'result': None,
                'error': f"执行异常: {str(e)}"
            }
    
    def _build_cookie_string(self) -> str:
        """构建cookie字符串"""
        return '; '.join([f"{k}={v}" for k, v in self.cookies.items()])
    
    def set_cookie(self, name: str, value: str):
        """设置Cookie"""
        self.cookies[name] = value
    
    def set_cookies_from_string(self, cookie_string: str):
        """从cookie字符串设置cookies"""
        for cookie_pair in cookie_string.split(';'):
            if '=' in cookie_pair:
                name, value = cookie_pair.strip().split('=', 1)
                if name and value:
                    self.cookies[name] = value
    
    def set_local_storage(self, key: str, value: str):
        """设置localStorage"""
        self.localStorage[key] = value
    
    def set_session_storage(self, key: str, value: str):
        """设置sessionStorage"""
        self.sessionStorage[key] = value


class OriginalJSGenerator:
    """使用原始JS代码的生成器"""
    
    def __init__(self, js_file_path: Optional[str] = None):
        self.executor = BrowserJSExecutor()
        self.original_js = ""
        
        if js_file_path and os.path.exists(js_file_path):
            self.original_js = self.executor.load_original_js_file(js_file_path)
        else:
            # 使用内置的示例JS代码
            self.original_js = self._get_sample_js()
    
    def _get_sample_js(self) -> str:
        """获取示例JS代码（模拟从浏览器扒下来的代码）"""
        return '''
// 这里放置从浏览器扒下来的原始JS代码
// 例如：vendor-dynamic.f0f5c43a.js 中的相关函数

// 示例：原始的xsCommon函数
function xsCommon(config, request) {
    // 原始的实现逻辑...
    var platform = config.platform || "PC";
    var url = request.url;
    
    // 检查是否需要签名
    var needsSign = url.indexOf("xiaohongshu.com") > -1;
    if (!needsSign) return request;
    
    // 获取签名计数
    var sigCount = parseInt(sessionStorage.getItem("sc") || "0");
    sigCount++;
    sessionStorage.setItem("sc", sigCount.toString());
    
    // 构建数据
    var commonData = {
        s0: platform === "PC" ? 5 : 1,
        s1: "",
        x1: "4.0.8",
        x2: platform,
        x3: "xhs-pc-web",
        x4: "4.68.0",
        x5: l.Z.get("a1") || "",
        x6: request.headers["X-t"] || "",
        x7: request.headers["X-s"] || "",
        x8: localStorage.getItem("b1") || "",
        x9: 0, // 这里应该是CRC32计算
        x10: sigCount,
        x11: "normal"
    };
    
    // 简化的Base64编码
    var jsonStr = JSON.stringify(commonData);
    var result = btoa(unescape(encodeURIComponent(jsonStr)));
    
    request.headers["X-S-Common"] = result;
    return request;
}
'''
    
    def set_user_data(self, user_data: Dict):
        """设置用户数据"""
        if 'a1' in user_data:
            self.executor.set_cookie('a1', user_data['a1'])
        
        if 'webId' in user_data:
            self.executor.set_local_storage('b1', user_data['webId'])
        
        if 'b1' in user_data:
            self.executor.set_local_storage('b1', user_data['b1'])
        
        if 'b1b1' in user_data:
            self.executor.set_local_storage('b1b1', user_data['b1b1'])
    
    def generate_xs_common(self, url: str, options: Optional[Dict] = None) -> Optional[str]:
        """生成x-s-common签名"""
        if options is None:
            options = {}
        
        params = {
            'url': url,
            'platform': options.get('platform', 'PC'),
            'xT': options.get('xT', ''),
            'xS': options.get('xS', ''),
            'xSign': options.get('xSign', ''),
            'options': options
        }
        
        result = self.executor.execute_js(self.original_js, 'generateXSCommon', params)
        
        if result['success']:
            return result['result']
        else:
            print(f"❌ 生成失败: {result.get('error', '未知错误')}")
            return None
    
    def call_js_function(self, function_name: str, *args) -> any:
        """调用JS中的任意函数"""
        params = {
            'functionName': function_name,
            'arguments': list(args)
        }
        
        result = self.executor.execute_js(self.original_js, 'callFunction', params)
        
        if result['success']:
            return result['result']
        else:
            print(f"❌ 函数调用失败: {result.get('error', '未知错误')}")
            return None


def test_original_js_generator():
    """测试原始JS生成器"""
    print("🚀 测试原始JS代码生成器")
    print("=" * 50)
    
    # 创建生成器
    generator = OriginalJSGenerator()
    
    # 设置用户数据
    generator.set_user_data({
        'a1': '19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152',
        'webId': '00ec9cba945a033e639b90fd1084ed06',
        'b1b1': '1'
    })
    
    print("✅ 用户数据设置完成")
    
    # 测试生成签名
    url = "https://www.xiaohongshu.com/api/sns/web/v1/feed"
    result = generator.generate_xs_common(url, {
        'platform': 'PC',
        'xT': str(int(time.time() * 1000)),
        'xS': 'XYW_test_signature'
    })
    
    if result:
        print(f"✅ 签名生成成功: {result[:50]}...")
    else:
        print("❌ 签名生成失败")


if __name__ == '__main__':
    test_original_js_generator()
