#!/usr/bin/env python3
"""
小红书 x-s-common 混合生成器
- JavaScript: 使用浏览器扒下来的原始算法
- Python: 提供环境补全和接口封装
"""

import json
import subprocess
import tempfile
import os
from typing import Dict, List, Union, Optional

class BrowserEnvironment:
    """Python模拟的浏览器环境"""
    
    def __init__(self):
        self.localStorage = {}
        self.sessionStorage = {}
        self.cookies = {}
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        
    def set_cookie(self, name: str, value: str):
        """设置Cookie"""
        self.cookies[name] = value
        
    def get_cookie(self, name: str) -> str:
        """获取Cookie"""
        return self.cookies.get(name, "")
        
    def set_local_storage(self, key: str, value: str):
        """设置localStorage"""
        self.localStorage[key] = value
        
    def get_local_storage(self, key: str) -> str:
        """获取localStorage"""
        return self.localStorage.get(key, "")
        
    def set_session_storage(self, key: str, value: str):
        """设置sessionStorage"""
        self.sessionStorage[key] = value
        
    def get_session_storage(self, key: str) -> str:
        """获取sessionStorage"""
        return self.sessionStorage.get(key, "")


class JSExecutor:
    """JavaScript执行器 - 运行原始的浏览器JS代码"""
    
    def __init__(self, env: BrowserEnvironment):
        self.env = env
        self.js_template = self._load_original_js()
        
    def _load_original_js(self) -> str:
        """加载原始的JavaScript代码"""
        # 这里是从浏览器扒下来的原始JS代码
        original_js = '''
// ==================== 原始浏览器JS代码 ====================

// 从浏览器扒下来的原始变量和函数
var S = [
    "fe_api/burdock/v2/user/keyInfo",
    "fe_api/burdock/v2/shield/profile", 
    "fe_api/burdock/v2/shield/captcha",
    "fe_api/burdock/v2/shield/registerCanvas",
    "api/sec/v1/shield/webprofile",
    "api/sec/v1/shield/captcha",
    /fe_api\\/burdock\\/v2\\/note\\/[0-9a-zA-Z]+\\/tags/,
    /fe_api\\/burdock\\/v2\\/note\\/[0-9a-zA-Z]+\\/image_stickers/,
    /fe_api\\/burdock\\/v2\\/note\\/[0-9a-zA-Z]+\\/other\\/notes/,
    /fe_api\\/burdock\\/v2\\/note\\/[0-9a-zA-Z]+\\/related/,
    "/fe_api/burdock/v2/note/post",
    "/api/sns/web",
    "/api/redcaptcha",
    "/api/store/jpd/main"
];

var k = []; // 实时URL数组

var g = [
    "/t.xiaohongshu.com",
    "/c.xiaohongshu.com", 
    "spltest.xiaohongshu.com",
    "t2.xiaohongshu.com",
    "t2-test.xiaohongshu.com",
    "lng.xiaohongshu.com",
    "apm-track.xiaohongshu.com",
    "apm-track-test.xiaohongshu.com",
    "fse.xiaohongshu.com",
    "fse.devops.xiaohongshu.com",
    "fesentry.xiaohongshu.com",
    "spider-tracker.xiaohongshu.com"
];

// 原始的平台代码枚举
var PlatformCode = {
    Android: 1,
    iOS: 2,
    MacOs: 3,
    Linux: 4,
    other: 5
};

var C = "4.0.8"; // 版本号

// 原始的Base64编码表
var code = "ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5";
var lookup = [];
for (var i = 0; i < code.length; i++) {
    lookup[i] = code.charAt(i);
}

// ==================== 原始核心算法函数 ====================

// 原始的CRC32算法 (O函数)
function O(e) {
    var ie = [
        0, 1996959894, 3993919788, 2567524794, 124634137, 1886057615, 3915621685,
        2657392035, 249268274, 2044508324, 3772115230, 2547177864, 162941995,
        2125561021, 3887607047, 2428444049, 498536548, 1789927666, 4089016648,
        2227061214, 450548861, 1843258603, 4107580753, 2211677639, 325883990,
        1684777152, 4251122042, 2321926636, 335633487, 1661365465, 4195302755,
        2366115317, 997073096, 1281953886, 3579855332, 2724688242, 1006888145,
        1258607687, 3524101629, 2768942443, 901097722, 1119000684, 3686517206,
        2898065728, 853044451, 1172266101, 3705015759, 2882616665, 651767980,
        1373503546, 3369554304, 3218104598, 565507253, 1454621731, 3485111705,
        3099436303, 671266974, 1594198024, 3322730930, 2970347812, 795835527,
        1483230225, 3244367275, 3060149565, 1994146192, 31158534, 2563907772,
        4023717930, 1907459465, 112637215, 2680153253, 3904427059, 2013776290,
        251722036, 2517215374, 3775830040, 2137656763, 141376813, 2439277719,
        3865271297, 1802195444, 476864866, 2238001368, 4066508878, 1812370925,
        453092731, 2181625025, 4111451223, 1706088902, 314042704, 2344532202,
        4240017532, 1658658271, 366619977, 2362670323, 4224994405, 1303535960,
        984961486, 2747007092, 3569037538, 1256170817, 1037604311, 2765210733,
        3554079995, 1131014506, 879679996, 2909243462, 3663771856, 1141124467,
        855842277, 2852801631, 3708648649, 1342533948, 654459306, 3188396048,
        3373015174, 1466479909, 544179635, 3110523913, 3462522015, 1591671054,
        702138776, 2966460450, 3352799412, 1504918807, 783551873, 3082640443,
        3233442989, 3988292384,
    ];

    function rightWithoutSign(num, bit) {
        bit = bit || 0;
        return (num >>> bit);
    }

    var o = -1;
    for (var n = 0; n < Math.min(57, e.length); n++) {
        o = ie[(o & 255) ^ e.charCodeAt(n)] ^ rightWithoutSign(o, 8);
    }
    return o ^ -1 ^ 3988292384;
}

// 原始的UTF8编码函数
function encodeUtf8(e) {
    var b = [];
    var m = encodeURIComponent(e);
    var w = 0;
    while (w < m.length) {
        var T = m.charAt(w);
        if (T === "%") {
            var E = m.charAt(w + 1) + m.charAt(w + 2);
            var S = parseInt(E, 16);
            b.push(S);
            w += 2;
        } else {
            b.push(T.charCodeAt(0));
        }
        w++;
    }
    return b;
}

// 原始的Base64编码函数
function b64Encode(e) {
    function tripletToBase64(e) {
        return (
            lookup[63 & (e >> 18)] + lookup[63 & (e >> 12)] + lookup[(e >> 6) & 63] + lookup[e & 63]
        );
    }

    function encodeChunk(e, t, r) {
        var m = [];
        for (var b = t; b < r; b += 3) {
            var n = (16711680 & (e[b] << 16)) + ((e[b + 1] << 8) & 65280) + (e[b + 2] & 255);
            m.push(tripletToBase64(n));
        }
        return m.join("");
    }

    var P = e.length;
    var W = P % 3;
    var U = [];
    var z = 16383;
    var H = 0;
    var Z = P - W;
    while (H < Z) {
        U.push(encodeChunk(e, H, Z < H + z ? Z : H + z));
        H += z;
    }
    if (1 === W) {
        var F = e[P - 1];
        U.push(lookup[F >> 2] + lookup[(F << 4) & 63] + "==");
    } else if (2 === W) {
        var F = (e[P - 2] << 8) + e[P - 1];
        U.push(lookup[F >> 10] + lookup[63 & (F >> 4)] + lookup[(F << 2) & 63] + "=");
    }
    return U.join("");
}

// ==================== 环境补全接口 ====================

// Python传入的环境数据
var pythonEnv = JSON.parse(arguments[0]);

// 模拟localStorage
var localStorage = {
    getItem: function(key) {
        return pythonEnv.localStorage[key] || null;
    },
    setItem: function(key, value) {
        pythonEnv.localStorage[key] = value;
    }
};

// 模拟sessionStorage  
var sessionStorage = {
    getItem: function(key) {
        return pythonEnv.sessionStorage[key] || null;
    },
    setItem: function(key, value) {
        pythonEnv.sessionStorage[key] = value;
    }
};

// 模拟cookie
var js_cookie = {
    A: {
        get: function(key) {
            return pythonEnv.cookies[key] || "";
        }
    }
};

// 模拟l.Z对象
var l = {
    Z: {
        get: function(key) {
            if (key === "a1") {
                return js_cookie.A.get(key);
            }
            return "";
        }
    }
};

// ==================== 主要生成函数 ====================

function generateXSCommon(url, options) {
    try {
        var platform = options.platform || "PC";
        var xT = options.xT || "";
        var xS = options.xS || "";
        
        // 检查URL是否需要签名
        var needsSign = false;
        for (var i = 0; i < S.length; i++) {
            var pattern = S[i];
            if (typeof pattern === 'string') {
                if (url.indexOf(pattern) > -1) {
                    needsSign = true;
                    break;
                }
            } else if (pattern instanceof RegExp) {
                if (pattern.test(url)) {
                    needsSign = true;
                    break;
                }
            }
        }
        
        if (!needsSign) {
            return null;
        }
        
        // 获取平台代码
        function getPlatformCode(platform) {
            switch (platform) {
                case "Android": return PlatformCode.Android;
                case "iOS": return PlatformCode.iOS;
                case "Mac OS": return PlatformCode.MacOs;
                case "Linux": return PlatformCode.Linux;
                default: return PlatformCode.other;
            }
        }
        
        // 获取签名计数
        var sigCount = parseInt(sessionStorage.getItem("sc") || "0");
        if (xT || xS) {
            sigCount++;
            sessionStorage.setItem("sc", sigCount.toString());
        }
        
        // 获取存储值
        var b1Value = localStorage.getItem("b1") || pythonEnv.deviceFingerprint;
        var b1b1Value = localStorage.getItem("b1b1") || "1";
        var a1Value = l.Z.get("a1") || "";
        
        // 构建数据结构
        var commonData = {
            s0: getPlatformCode(platform),
            s1: "",
            x0: b1b1Value,
            x1: C,
            x2: platform || "PC", 
            x3: "xhs-pc-web",
            x4: "4.68.0",
            x5: a1Value,
            x6: xT,
            x7: xS,
            x8: b1Value,
            x9: O(xT + xS + b1Value),
            x10: sigCount,
            x11: "normal"
        };
        
        // 生成最终结果
        var jsonStr = JSON.stringify(commonData);
        var utf8Bytes = encodeUtf8(jsonStr);
        var result = b64Encode(utf8Bytes);
        
        return result;
        
    } catch (error) {
        return null;
    }
}

// 执行并返回结果
var params = JSON.parse(arguments[1]);
var result = generateXSCommon(params.url, params.options);
console.log(JSON.stringify({
    success: result !== null,
    result: result,
    env: pythonEnv
}));
'''
        return original_js
        
    def execute_js(self, url: str, options: Dict) -> Optional[str]:
        """执行JavaScript代码生成签名"""
        try:
            # 准备环境数据
            env_data = {
                'localStorage': self.env.localStorage,
                'sessionStorage': self.env.sessionStorage, 
                'cookies': self.env.cookies,
                'deviceFingerprint': self.env.get_local_storage('b1') or self._generate_device_fingerprint(),
                'userAgent': self.env.user_agent
            }
            
            # 准备参数
            params = {
                'url': url,
                'options': options
            }
            
            # 创建临时JS文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False, encoding='utf-8') as f:
                f.write(self.js_template)
                js_file = f.name
            
            try:
                # 执行JavaScript
                cmd = ['node', js_file, json.dumps(env_data), json.dumps(params)]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    output = json.loads(result.stdout.strip())
                    if output['success']:
                        # 更新环境状态
                        self.env.localStorage.update(output['env']['localStorage'])
                        self.env.sessionStorage.update(output['env']['sessionStorage'])
                        return output['result']
                
                return None
                
            finally:
                # 清理临时文件
                if os.path.exists(js_file):
                    os.unlink(js_file)
                    
        except Exception as e:
            print(f"JavaScript执行失败: {e}")
            return None
    
    def _generate_device_fingerprint(self) -> str:
        """生成设备指纹"""
        import time
        import hashlib
        
        timestamp = str(int(time.time() * 1000))
        device_info = "Windows-Chrome-120.0.0.0"
        combined = f"{device_info}-{timestamp}"
        
        hash_obj = hashlib.md5(combined.encode())
        return hash_obj.hexdigest()[:16]


class HybridXSCommonGenerator:
    """混合式x-s-common生成器"""
    
    def __init__(self):
        self.env = BrowserEnvironment()
        self.js_executor = JSExecutor(self.env)
        
    def set_user_data(self, user_data: Dict):
        """设置用户数据"""
        if 'a1' in user_data:
            self.env.set_cookie('a1', user_data['a1'])
            
        if 'b1' in user_data:
            self.env.set_local_storage('b1', user_data['b1'])
            
        if 'b1b1' in user_data:
            self.env.set_local_storage('b1b1', user_data['b1b1'])
            
        if 'webId' in user_data:
            self.env.set_local_storage('b1', user_data['webId'])
    
    def generate(self, url: str, options: Optional[Dict] = None) -> Optional[str]:
        """生成x-s-common签名"""
        if options is None:
            options = {}
            
        return self.js_executor.execute_js(url, options)
    
    def parse_cookie(self, cookie_string: str) -> Dict[str, str]:
        """解析cookie字符串"""
        cookies = {}
        for cookie_pair in cookie_string.split(';'):
            if '=' in cookie_pair:
                name, value = cookie_pair.strip().split('=', 1)
                if name and value:
                    cookies[name] = value
                    self.env.set_cookie(name, value)
        return cookies


# ==================== 使用示例 ====================

def test_hybrid_generator():
    """测试混合生成器"""
    print("🚀 测试混合式 x-s-common 生成器")
    print("=" * 60)
    
    # 创建生成器
    generator = HybridXSCommonGenerator()
    
    # 设置用户数据
    real_cookie = ("a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; "
                   "webId=00ec9cba945a033e639b90fd1084ed06; "
                   "gid=yjWJjS02JiIYyjWJjS0qd6TkfjiWy0UCVuC2V763I00KyE28CkS0FD8882KYy2J804SS4KWf")
    
    cookies = generator.parse_cookie(real_cookie)
    generator.set_user_data({
        'a1': cookies.get('a1', ''),
        'b1': cookies.get('webId', ''),
        'b1b1': '1'
    })
    
    print("✅ 用户数据设置完成")
    
    # 测试URL
    test_urls = [
        "https://www.xiaohongshu.com/api/sns/web/v1/feed",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/note/post"
    ]
    
    for i, url in enumerate(test_urls):
        print(f"\n📝 测试 {i+1}: {url}")
        
        result = generator.generate(url, {
            'platform': 'PC',
            'xT': str(int(time.time() * 1000)),
            'xS': f'XYW_test_{i}'
        })
        
        if result:
            print(f"✅ 生成成功: {result[:50]}...")
        else:
            print(f"❌ 生成失败")


if __name__ == '__main__':
    import time
    test_hybrid_generator()
