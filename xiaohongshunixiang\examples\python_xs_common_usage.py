#!/usr/bin/env python3
"""
小红书 x-s-common Python版本使用示例
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.xs_common_generator import (
    generate_xs_common, 
    set_user_data, 
    parse_cookie,
    test,
    test_single_url
)

def example_basic_usage():
    """基础使用示例"""
    print("🚀 Python版本 x-s-common 基础使用示例")
    print("=" * 60)
    
    # 1. 设置用户数据
    print("\n📋 步骤1: 设置用户数据")
    user_cookie = ("a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; "
                   "webId=00ec9cba945a033e639b90fd1084ed06; "
                   "gid=yjWJjS02JiIYyjWJjS0qd6TkfjiWy0UCVuC2V763I00KyE28CkS0FD8882KYy2J804SS4KWf")
    
    cookies = parse_cookie(user_cookie)
    
    set_user_data({
        'a1': cookies.get('a1', ''),
        'b1': cookies.get('webId', ''),  # 使用webId作为设备指纹
        'b1b1': "1"
    })
    
    print(f"✅ 用户数据设置完成")
    print(f"   a1: {cookies.get('a1', 'N/A')[:20]}...")
    print(f"   webId: {cookies.get('webId', 'N/A')}")
    
    # 2. 生成签名
    print("\n🔧 步骤2: 生成 x-s-common 签名")
    
    test_url = "https://www.xiaohongshu.com/api/sns/web/v1/feed"
    
    xs_common = generate_xs_common(test_url, {
        'platform': 'PC',
        'xT': '1750000000000',  # 时间戳
        'xS': 'XYW_test_signature_value'  # X-s值
    })
    
    if xs_common:
        print(f"✅ 签名生成成功:")
        print(f"   URL: {test_url}")
        print(f"   X-S-Common: {xs_common[:50]}...")
        print(f"   完整长度: {len(xs_common)} 字符")
        
        # 3. 验证签名
        print("\n🔍 步骤3: 验证签名结构")
        try:
            import base64
            decoded = base64.b64decode(xs_common).decode('utf-8')
            parsed = json.loads(decoded)
            
            print(f"📊 签名结构验证:")
            print(f"   平台代码 (s0): {parsed.get('s0')}")
            print(f"   版本 (x1): {parsed.get('x1')}")
            print(f"   平台 (x2): {parsed.get('x2')}")
            print(f"   应用ID (x3): {parsed.get('x3')}")
            print(f"   应用版本 (x4): {parsed.get('x4')}")
            print(f"   用户ID (x5): {parsed.get('x5', '')[:20] + '...' if parsed.get('x5') else '空'}")
            print(f"   设备指纹 (x8): {parsed.get('x8', '')[:20] + '...' if parsed.get('x8') else '空'}")
            print(f"   CRC32 (x9): {parsed.get('x9')}")
            print(f"   计数 (x10): {parsed.get('x10')}")
            print(f"   状态 (x11): {parsed.get('x11')}")
            
        except Exception as e:
            print(f"❌ 签名验证失败: {e}")
    else:
        print(f"❌ 签名生成失败")


def example_batch_generation():
    """批量生成示例"""
    print("\n📦 批量生成示例")
    print("-" * 40)
    
    # 设置用户数据
    user_cookie = "a1=test_a1_value; webId=test_web_id"
    cookies = parse_cookie(user_cookie)
    
    set_user_data({
        'a1': cookies.get('a1', ''),
        'b1': cookies.get('webId', ''),
        'b1b1': "1"
    })
    
    # 批量URL列表
    test_urls = [
        "https://www.xiaohongshu.com/api/sns/web/v1/feed",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/note/post",
        "https://www.xiaohongshu.com/api/sec/v1/shield/webprofile",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/user/keyInfo",
        "https://www.xiaohongshu.com/api/sns/web/v1/search/notes"
    ]
    
    print(f"🔧 批量生成 {len(test_urls)} 个URL的签名...")
    
    results = []
    for i, url in enumerate(test_urls):
        print(f"\n📝 处理 {i+1}/{len(test_urls)}: {url}")
        
        xs_common = generate_xs_common(url, {
            'platform': 'PC',
            'xT': str(1750000000000 + i),  # 递增时间戳
            'xS': f'XYW_batch_test_{i}'
        })
        
        if xs_common:
            results.append({
                'url': url,
                'xs_common': xs_common,
                'success': True
            })
            print(f"   ✅ 成功: {xs_common[:30]}...")
        else:
            results.append({
                'url': url,
                'xs_common': None,
                'success': False
            })
            print(f"   ❌ 失败")
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    print(f"\n📊 批量生成结果:")
    print(f"   总数: {len(results)}")
    print(f"   成功: {success_count}")
    print(f"   失败: {len(results) - success_count}")
    print(f"   成功率: {success_count/len(results)*100:.1f}%")


def example_platform_comparison():
    """不同平台对比示例"""
    print("\n🔄 不同平台对比示例")
    print("-" * 40)
    
    # 设置用户数据
    set_user_data({
        'a1': 'test_a1_value',
        'b1': 'test_device_fingerprint',
        'b1b1': "1"
    })
    
    test_url = "https://www.xiaohongshu.com/api/sns/web/v1/feed"
    platforms = ['PC', 'Android', 'iOS', 'Mac OS', 'Linux']
    
    print(f"🔧 测试URL: {test_url}")
    print(f"🔧 测试平台: {', '.join(platforms)}")
    
    for platform in platforms:
        print(f"\n📱 平台: {platform}")
        
        xs_common = generate_xs_common(test_url, {
            'platform': platform,
            'xT': '1750000000000',
            'xS': 'XYW_platform_test'
        })
        
        if xs_common:
            try:
                import base64
                decoded = base64.b64decode(xs_common).decode('utf-8')
                parsed = json.loads(decoded)
                
                print(f"   ✅ 生成成功")
                print(f"   平台代码 (s0): {parsed.get('s0')}")
                print(f"   平台名称 (x2): {parsed.get('x2')}")
                print(f"   签名长度: {len(xs_common)}")
                
            except Exception as e:
                print(f"   ❌ 解析失败: {e}")
        else:
            print(f"   ❌ 生成失败")


def example_performance_test():
    """性能测试示例"""
    print("\n⚡ 性能测试示例")
    print("-" * 40)
    
    import time
    
    # 设置用户数据
    set_user_data({
        'a1': 'performance_test_a1',
        'b1': 'performance_test_b1',
        'b1b1': "1"
    })
    
    test_url = "https://www.xiaohongshu.com/api/sns/web/v1/feed"
    test_count = 100
    
    print(f"🔧 性能测试: 生成 {test_count} 次签名")
    print(f"🔧 测试URL: {test_url}")
    
    start_time = time.time()
    success_count = 0
    
    for i in range(test_count):
        xs_common = generate_xs_common(test_url, {
            'platform': 'PC',
            'xT': str(int(time.time() * 1000) + i),
            'xS': f'XYW_perf_test_{i}'
        })
        
        if xs_common:
            success_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n📊 性能测试结果:")
    print(f"   总耗时: {total_time:.3f} 秒")
    print(f"   平均耗时: {total_time/test_count*1000:.2f} 毫秒/次")
    print(f"   成功次数: {success_count}/{test_count}")
    print(f"   成功率: {success_count/test_count*100:.1f}%")
    print(f"   吞吐量: {test_count/total_time:.1f} 次/秒")


def main():
    """主函数"""
    print("🐍 小红书 x-s-common Python版本使用示例")
    print("=" * 80)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_batch_generation()
        example_platform_comparison()
        example_performance_test()
        
        print("\n" + "=" * 80)
        print("🎉 所有示例运行完成!")
        
        print("\n💡 使用提示:")
        print("1. 确保设置正确的 a1 cookie 值")
        print("2. b1 值应该是稳定的设备指纹")
        print("3. 不同平台会生成不同的平台代码")
        print("4. Python版本性能优于JavaScript版本")
        print("5. 可以通过命令行直接测试单个URL")
        
        print("\n🔧 命令行使用:")
        print("python xs_common_generator.py <URL> [platform]")
        print("python python_xs_common_usage.py")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
